package vertexai

import (
	"done-hub/common"
	"done-hub/common/requester"
	"done-hub/providers/claude"
	"done-hub/providers/gemini"
	"done-hub/providers/vertexai/category"
	"done-hub/types"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
)

func (p *VertexAIProvider) CreateClaudeChat(request *claude.ClaudeRequest) (*claude.ClaudeResponse, *types.OpenAIErrorWithStatusCode) {
	// 检查是否为 Gemini 模型，如果是则进行格式转换
	if p.isGeminiModel(request.Model) {
		return p.createClaudeChatWithGeminiConversion(request)
	}

	req, errWithCode := p.getClaudeRequest(request)
	if errWithCode != nil {
		return nil, errWithCode
	}
	defer req.Body.Close()

	claudeResponse := &claude.ClaudeResponse{}
	// // 发送请求
	_, openaiErr := p.Requester.SendRequest(req, claudeResponse, false)
	if openaiErr != nil {
		return nil, openaiErr
	}

	claude.ClaudeUsageToOpenaiUsage(&claudeResponse.Usage, p.GetUsage())

	return claudeResponse, nil
}

func (p *VertexAIProvider) CreateClaudeChatStream(request *claude.ClaudeRequest) (requester.StreamReaderInterface[string], *types.OpenAIErrorWithStatusCode) {
	// 检查是否为 Gemini 模型，如果是则进行格式转换
	if p.isGeminiModel(request.Model) {
		return p.createClaudeChatStreamWithGeminiConversion(request)
	}

	req, errWithCode := p.getClaudeRequest(request)
	if errWithCode != nil {
		return nil, errWithCode
	}
	defer req.Body.Close()

	chatHandler := &claude.ClaudeRelayStreamHandler{
		Usage:     p.Usage,
		ModelName: request.Model,
		Prefix:    `data: {"type"`,
	}

	// 发送请求
	resp, openaiErr := p.Requester.SendRequestRaw(req)
	if openaiErr != nil {
		return nil, openaiErr
	}

	stream, openaiErr := requester.RequestNoTrimStream(p.Requester, resp, chatHandler.HandlerStream)
	if openaiErr != nil {
		return nil, openaiErr
	}

	return stream, nil
}

func (p *VertexAIProvider) getClaudeRequest(request *claude.ClaudeRequest) (*http.Request, *types.OpenAIErrorWithStatusCode) {
	var err error
	p.Category, err = category.GetCategory(request.Model)
	if err != nil || p.Category.Category != "claude" {
		return nil, common.StringErrorWrapperLocal("vertexAI provider not found", "vertexAI_err", http.StatusInternalServerError)
	}

	otherUrl := p.Category.GetOtherUrl(request.Stream)
	modelName := p.Category.GetModelName(request.Model)

	// 获取请求地址
	fullRequestURL := p.GetFullRequestURL(modelName, otherUrl)
	if fullRequestURL == "" {
		return nil, common.StringErrorWrapperLocal("vertexAI config error", "invalid_vertexai_config", http.StatusInternalServerError)
	}

	headers := p.GetRequestHeaders()

	if headers == nil {
		return nil, common.StringErrorWrapperLocal("vertexAI config error", "invalid_vertexai_config", http.StatusInternalServerError)
	}

	if request.Stream {
		headers["Accept"] = "text/event-stream"
	}

	copyRequest := *request

	vertexaiRequest := &category.ClaudeRequest{
		ClaudeRequest:    &copyRequest,
		AnthropicVersion: category.AnthropicVersion,
	}
	vertexaiRequest.Model = ""

	// 错误处理
	p.Requester.ErrorHandler = RequestErrorHandle(p.Category.ErrorHandler)

	// 使用BaseProvider的统一方法创建请求，支持额外参数处理
	req, errWithCode := p.NewRequestWithCustomParams(http.MethodPost, fullRequestURL, vertexaiRequest, headers, request.Model)
	if errWithCode != nil {
		return nil, errWithCode
	}
	return req, nil
}

// isGeminiModel 检查模型是否为 Gemini 模型
func (p *VertexAIProvider) isGeminiModel(modelName string) bool {
	return strings.HasPrefix(modelName, "gemini")
}

// createClaudeChatWithGeminiConversion 使用 Gemini 转换处理 Claude 请求
func (p *VertexAIProvider) createClaudeChatWithGeminiConversion(request *claude.ClaudeRequest) (*claude.ClaudeResponse, *types.OpenAIErrorWithStatusCode) {
	// 1. 直接将 Claude 请求转换为 Gemini 格式
	geminiRequest, err := p.convertClaudeToGemini(request)
	if err != nil {
		return nil, err
	}

	// 2. 获取 Gemini 请求
	req, errWithCode := p.getGeminiRequestForClaude(geminiRequest, request.Model, false)
	if errWithCode != nil {
		return nil, errWithCode
	}
	defer req.Body.Close()

	// 3. 发送请求到 Vertex AI Gemini 接口
	geminiResponse := &gemini.GeminiChatResponse{}
	_, openaiErr := p.Requester.SendRequest(req, geminiResponse, false)
	if openaiErr != nil {
		return nil, openaiErr
	}

	// 4. 直接将 Gemini 响应转换为 Claude 格式
	claudeResponse := p.convertGeminiToClaude(geminiResponse, request.Model)

	return claudeResponse, nil
}

// createClaudeChatStreamWithGeminiConversion 使用 Gemini 转换处理 Claude 流式请求
func (p *VertexAIProvider) createClaudeChatStreamWithGeminiConversion(request *claude.ClaudeRequest) (requester.StreamReaderInterface[string], *types.OpenAIErrorWithStatusCode) {
	// 1. 直接将 Claude 请求转换为 Gemini 格式
	geminiRequest, err := p.convertClaudeToGemini(request)
	if err != nil {
		return nil, err
	}

	// 2. 获取 Gemini 流式请求
	req, errWithCode := p.getGeminiRequestForClaude(geminiRequest, request.Model, true)
	if errWithCode != nil {
		return nil, errWithCode
	}
	defer req.Body.Close()

	// 3. 发送请求到 Vertex AI Gemini 接口
	resp, openaiErr := p.Requester.SendRequestRaw(req)
	if openaiErr != nil {
		return nil, openaiErr
	}

	// 4. 创建流式处理器，将 Gemini 流直接转换为 Claude 流
	streamHandler := &VertexGeminiToClaudeStreamHandler{
		Usage:     p.Usage,
		ModelName: request.Model,
	}

	return requester.RequestStream(p.Requester, resp, streamHandler.HandlerStream)
}

// convertClaudeToGemini 直接将 Claude 请求转换为 Gemini 格式
func (p *VertexAIProvider) convertClaudeToGemini(claudeRequest *claude.ClaudeRequest) (*gemini.GeminiChatRequest, *types.OpenAIErrorWithStatusCode) {
	geminiRequest := &gemini.GeminiChatRequest{
		Contents: make([]gemini.GeminiChatContent, 0),
		SafetySettings: []gemini.GeminiChatSafetySettings{
			{Category: "HARM_CATEGORY_HARASSMENT", Threshold: "BLOCK_NONE"},
			{Category: "HARM_CATEGORY_HATE_SPEECH", Threshold: "BLOCK_NONE"},
			{Category: "HARM_CATEGORY_SEXUALLY_EXPLICIT", Threshold: "BLOCK_NONE"},
			{Category: "HARM_CATEGORY_DANGEROUS_CONTENT", Threshold: "BLOCK_NONE"},
			{Category: "HARM_CATEGORY_CIVIC_INTEGRITY", Threshold: "BLOCK_NONE"},
		},
		GenerationConfig: gemini.GeminiChatGenerationConfig{
			Temperature:     claudeRequest.Temperature,
			TopP:            claudeRequest.TopP,
			MaxOutputTokens: claudeRequest.MaxTokens,
		},
		Stream: claudeRequest.Stream,
		Model:  claudeRequest.Model,
	}

	// 处理 system 消息
	if claudeRequest.System != nil {
		systemContent := ""
		switch system := claudeRequest.System.(type) {
		case string:
			systemContent = system
		case []interface{}:
			// 处理复杂的 system 内容
			for _, item := range system {
				if itemMap, ok := item.(map[string]interface{}); ok {
					if itemType, exists := itemMap["type"]; exists && itemType == "text" {
						if text, textExists := itemMap["text"]; textExists {
							if textStr, ok := text.(string); ok {
								systemContent += textStr
							}
						}
					}
				}
			}
		}

		if systemContent != "" {
			geminiRequest.SystemInstruction = &gemini.GeminiChatContent{
				Parts: []gemini.GeminiPart{
					{Text: systemContent},
				},
			}
		}
	}

	// 转换消息
	for _, message := range claudeRequest.Messages {
		geminiRole := "user"
		if message.Role == "assistant" {
			geminiRole = "model"
		}

		var parts []gemini.GeminiPart

		// 处理消息内容
		switch content := message.Content.(type) {
		case string:
			parts = append(parts, gemini.GeminiPart{Text: content})
		case []interface{}:
			// 处理复杂内容（文本、图片、工具调用等）

			for _, item := range content {
				if itemMap, ok := item.(map[string]interface{}); ok {
					itemType, _ := itemMap["type"].(string)

					switch itemType {
					case "text":
						if text, exists := itemMap["text"]; exists {
							if textStr, ok := text.(string); ok {
								parts = append(parts, gemini.GeminiPart{Text: textStr})
							}
						}
					case "image":
						// 处理图片内容
						if source, exists := itemMap["source"]; exists {
							if sourceMap, ok := source.(map[string]interface{}); ok {
								mediaType, _ := sourceMap["media_type"].(string)
								data, _ := sourceMap["data"].(string)

								parts = append(parts, gemini.GeminiPart{
									InlineData: &gemini.GeminiInlineData{
										MimeType: mediaType,
										Data:     data,
									},
								})
							}
						}
					case "tool_use":
						// 处理工具调用
						if name, nameExists := itemMap["name"]; nameExists {
							if input, inputExists := itemMap["input"]; inputExists {
								// 确保 input 是 map[string]interface{} 类型
								var args map[string]interface{}
								if inputMap, ok := input.(map[string]interface{}); ok {
									args = inputMap
								} else {
									// 如果不是 map，尝试转换
									inputBytes, _ := json.Marshal(input)
									json.Unmarshal(inputBytes, &args)
								}

								parts = append(parts, gemini.GeminiPart{
									FunctionCall: &gemini.GeminiFunctionCall{
										Name: name.(string),
										Args: args,
									},
								})
							}
						}
					case "tool_result":
						// 处理工具结果
						if content, contentExists := itemMap["content"]; contentExists {
							contentStr := ""
							if str, ok := content.(string); ok {
								contentStr = str
							} else {
								contentBytes, _ := json.Marshal(content)
								contentStr = string(contentBytes)
							}

							parts = append(parts, gemini.GeminiPart{
								FunctionResponse: &gemini.GeminiFunctionResponse{
									Name: "tool_result", // Gemini 需要函数名
									Response: map[string]interface{}{
										"result": contentStr,
									},
								},
							})
						}
					}
				}
			}
		}

		// 添加到 Gemini 内容中
		if len(parts) > 0 {
			geminiRequest.Contents = append(geminiRequest.Contents, gemini.GeminiChatContent{
				Role:  geminiRole,
				Parts: parts,
			})
		}
	}

	// 转换工具定义
	if claudeRequest.Tools != nil && len(claudeRequest.Tools) > 0 {
		var functionDeclarations []types.ChatCompletionFunction
		for _, tool := range claudeRequest.Tools {
			if tool.Type == "function" {
				functionDeclarations = append(functionDeclarations, types.ChatCompletionFunction{
					Name:        tool.Name,
					Description: tool.Description,
					Parameters:  tool.InputSchema,
				})
			}
		}

		if len(functionDeclarations) > 0 {
			geminiRequest.Tools = []gemini.GeminiChatTools{
				{
					FunctionDeclarations: functionDeclarations,
				},
			}
		}
	}

	// 转换工具选择 (Gemini 的工具选择相对简单，主要通过 tools 的存在来控制)
	// Claude 的 tool_choice 在 Gemini 中通过不同的方式处理
	if claudeRequest.ToolChoice != nil {
		switch claudeRequest.ToolChoice.Type {
		case "tool":
			// 如果指定了特定工具，可以通过只包含该工具来实现
			if claudeRequest.ToolChoice.Name != "" {
				// 过滤工具，只保留指定的工具
				var filteredDeclarations []types.ChatCompletionFunction
				for _, tool := range geminiRequest.Tools {
					for _, decl := range tool.FunctionDeclarations {
						if decl.Name == claudeRequest.ToolChoice.Name {
							filteredDeclarations = append(filteredDeclarations, decl)
						}
					}
				}
				if len(filteredDeclarations) > 0 {
					geminiRequest.Tools = []gemini.GeminiChatTools{
						{FunctionDeclarations: filteredDeclarations},
					}
				}
			}
		}
	}

	return geminiRequest, nil
}

// convertGeminiToClaude 直接将 Gemini 响应转换为 Claude 格式
func (p *VertexAIProvider) convertGeminiToClaude(geminiResponse *gemini.GeminiChatResponse, modelName string) *claude.ClaudeResponse {
	claudeResponse := &claude.ClaudeResponse{
		Id:      geminiResponse.ResponseId,
		Type:    "message",
		Role:    "assistant",
		Model:   modelName,
		Content: make([]claude.ResContent, 0),
	}

	if len(geminiResponse.Candidates) > 0 {
		candidate := geminiResponse.Candidates[0]

		// 处理内容部分
		for _, part := range candidate.Content.Parts {
			if part.Text != "" {
				claudeResponse.Content = append(claudeResponse.Content, claude.ResContent{
					Type: "text",
					Text: part.Text,
				})
			}

			// 处理函数调用
			if part.FunctionCall != nil {
				claudeResponse.Content = append(claudeResponse.Content, claude.ResContent{
					Type:  "tool_use",
					Id:    fmt.Sprintf("tool_%d", len(claudeResponse.Content)),
					Name:  part.FunctionCall.Name,
					Input: part.FunctionCall.Args,
				})
			}
		}

		// 转换停止原因
		if candidate.FinishReason != nil {
			switch *candidate.FinishReason {
			case "STOP":
				claudeResponse.StopReason = "end_turn"
			case "MAX_TOKENS":
				claudeResponse.StopReason = "max_tokens"
			case "SAFETY":
				claudeResponse.StopReason = "stop_sequence"
			case "RECITATION":
				claudeResponse.StopReason = "stop_sequence"
			default:
				claudeResponse.StopReason = "end_turn"
			}
		} else {
			claudeResponse.StopReason = "end_turn"
		}
	}

	// 转换使用情况
	if geminiResponse.UsageMetadata != nil {
		claudeResponse.Usage = claude.Usage{
			InputTokens:  geminiResponse.UsageMetadata.PromptTokenCount,
			OutputTokens: geminiResponse.UsageMetadata.CandidatesTokenCount,
		}
	}

	return claudeResponse
}

// getGeminiRequestForClaude 构建 Gemini 请求（用于 Claude 转换）
func (p *VertexAIProvider) getGeminiRequestForClaude(geminiRequest *gemini.GeminiChatRequest, modelName string, isStream bool) (*http.Request, *types.OpenAIErrorWithStatusCode) {
	var err error
	p.Category, err = category.GetCategory(modelName)
	if err != nil {
		return nil, common.StringErrorWrapperLocal("vertexAI provider not found", "vertexAI_err", http.StatusInternalServerError)
	}

	otherUrl := p.Category.GetOtherUrl(isStream)
	geminiModelName := p.Category.GetModelName(modelName)

	// 获取请求地址
	fullRequestURL := p.GetFullRequestURL(geminiModelName, otherUrl)
	if fullRequestURL == "" {
		return nil, common.StringErrorWrapperLocal("vertexAI config error", "invalid_vertexai_config", http.StatusInternalServerError)
	}

	headers := p.GetRequestHeaders()
	if headers == nil {
		return nil, common.StringErrorWrapperLocal("vertexAI config error", "invalid_vertexai_config", http.StatusInternalServerError)
	}

	if isStream {
		headers["Accept"] = "text/event-stream"
	}

	// 错误处理
	p.Requester.ErrorHandler = RequestErrorHandle(p.Category.ErrorHandler)

	// 使用BaseProvider的统一方法创建请求
	req, errWithCode := p.NewRequestWithCustomParams(http.MethodPost, fullRequestURL, geminiRequest, headers, modelName)
	if errWithCode != nil {
		return nil, errWithCode
	}
	return req, nil
}

// VertexGeminiToClaudeStreamHandler 处理 Gemini 流式响应转换为 Claude 格式
type VertexGeminiToClaudeStreamHandler struct {
	Usage     *types.Usage
	ModelName string
}

// HandlerStream 处理流式数据转换
func (h *VertexGeminiToClaudeStreamHandler) HandlerStream(rawLine *[]byte, dataChan chan string, errChan chan error) {
	// 如果rawLine 前缀不为data:，则直接返回
	if !strings.HasPrefix(string(*rawLine), "data: ") {
		*rawLine = nil
		return
	}

	// 去除前缀
	dataStr := strings.TrimPrefix(string(*rawLine), "data: ")
	dataStr = strings.TrimSpace(dataStr)

	if dataStr == "" || dataStr == "[DONE]" {
		*rawLine = nil
		return
	}

	// 解析 Gemini 流式响应
	var geminiStreamResponse gemini.GeminiChatResponse
	if err := json.Unmarshal([]byte(dataStr), &geminiStreamResponse); err != nil {
		*rawLine = nil
		return
	}

	// 直接转换为 Claude 流式格式
	claudeStreamData := h.convertGeminiStreamToClaude(&geminiStreamResponse)
	if claudeStreamData != "" {
		dataChan <- claudeStreamData
	}

	*rawLine = nil
}

// convertGeminiStreamToClaude 直接将 Gemini 流式响应转换为 Claude 流式格式
func (h *VertexGeminiToClaudeStreamHandler) convertGeminiStreamToClaude(geminiResponse *gemini.GeminiChatResponse) string {
	if len(geminiResponse.Candidates) == 0 {
		return ""
	}

	candidate := geminiResponse.Candidates[0]

	// 处理文本内容
	for _, part := range candidate.Content.Parts {
		if part.Text != "" {
			claudeStream := map[string]interface{}{
				"type": "content_block_delta",
				"index": 0,
				"delta": map[string]interface{}{
					"type": "text_delta",
					"text": part.Text,
				},
			}
			claudeBytes, _ := json.Marshal(claudeStream)
			return string(claudeBytes)
		}

		// 处理函数调用
		if part.FunctionCall != nil {
			claudeStream := map[string]interface{}{
				"type": "content_block_start",
				"index": 0,
				"content_block": map[string]interface{}{
					"type":  "tool_use",
					"id":    fmt.Sprintf("tool_%d", 0),
					"name":  part.FunctionCall.Name,
					"input": part.FunctionCall.Args,
				},
			}
			claudeBytes, _ := json.Marshal(claudeStream)
			return string(claudeBytes)
		}
	}

	// 处理完成状态
	if candidate.FinishReason != nil && *candidate.FinishReason != "" {
		claudeStream := map[string]interface{}{
			"type": "message_delta",
			"delta": map[string]interface{}{
				"stop_reason": h.convertGeminiFinishReason(*candidate.FinishReason),
			},
		}
		claudeBytes, _ := json.Marshal(claudeStream)
		return string(claudeBytes)
	}

	return ""
}

// convertGeminiFinishReason 转换 Gemini 完成原因为 Claude 格式
func (h *VertexGeminiToClaudeStreamHandler) convertGeminiFinishReason(finishReason string) string {
	switch finishReason {
	case "STOP":
		return "end_turn"
	case "MAX_TOKENS":
		return "max_tokens"
	case "SAFETY":
		return "stop_sequence"
	case "RECITATION":
		return "stop_sequence"
	default:
		return "end_turn"
	}
}
