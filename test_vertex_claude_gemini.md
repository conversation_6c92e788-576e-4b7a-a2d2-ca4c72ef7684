# Vertex AI Claude-Gemini 转换功能测试

## 功能说明

在 Vertex AI 渠道下，当接收到 `/claude/v1/messages` 接口请求时，如果模型是 Gemini 模型（以 `gemini` 开头），系统会自动进行**直接格式转换**：

1. **Claude 请求** → **Gemini 格式**
2. **请求 Vertex AI Gemini 接口**
3. **Gemini 响应** → **Claude 响应**

## 实现的功能

### 1. 模型检测
- 通过 `isGeminiModel()` 方法检测模型名称是否以 `gemini` 开头
- 如果是 Gemini 模型，则启用转换逻辑

### 2. 请求转换
- **Claude → Gemini**: `convertClaudeToGemini()`
  - 直接转换消息格式（system、user、assistant、tool）
  - 处理复杂内容（文本、图片、工具调用）
  - 转换工具定义和工具选择
  - 设置安全配置和生成参数

### 3. 响应转换
- **Gemini → Claude**: `convertGeminiToClaude()`
  - 直接转换响应格式
  - 处理工具调用结果
  - 转换停止原因和使用统计

### 4. 流式处理
- 实现 `VertexGeminiToClaudeStreamHandler` 处理流式响应转换
- 直接将 Gemini 流式数据转换为 Claude 流式格式
- 支持文本增量、工具调用和完成状态的实时转换

## 测试用例

### 基础文本对话
```bash
curl -X POST http://localhost:3000/claude/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 1000,
    "messages": [
      {
        "role": "user",
        "content": "Hello, how are you?"
      }
    ]
  }'
```

### 流式对话
```bash
curl -X POST http://localhost:3000/claude/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 1000,
    "stream": true,
    "messages": [
      {
        "role": "user",
        "content": "Tell me a story"
      }
    ]
  }'
```

### 工具调用
```bash
curl -X POST http://localhost:3000/claude/v1/messages \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "model": "gemini-1.5-pro",
    "max_tokens": 1000,
    "messages": [
      {
        "role": "user",
        "content": "What is the weather like today?"
      }
    ],
    "tools": [
      {
        "type": "function",
        "name": "get_weather",
        "description": "Get current weather",
        "input_schema": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The location to get weather for"
            }
          },
          "required": ["location"]
        }
      }
    ]
  }'
```

## 工作流程

```
客户端 Claude 请求 → Vertex AI Provider → 模型检测
                                           ↓ (如果是 Gemini)
Claude 格式 → Gemini 格式 → Vertex AI Gemini API
                             ↓
Claude 响应 ← Gemini 格式 ← Vertex AI 响应
```

## 验证要点

1. **模型检测**: 确保只有 `gemini` 开头的模型才会触发转换
2. **格式转换**: 验证请求和响应格式正确转换
3. **流式处理**: 确保流式响应正确转换为 Claude 格式
4. **工具调用**: 验证工具定义和调用结果正确转换
5. **错误处理**: 确保转换过程中的错误能正确处理和返回

## 注意事项

- 该功能仅在 Vertex AI 渠道下生效
- 只有模型名称以 `gemini` 开头才会触发转换
- 转换过程中会保持原有的流式/非流式状态
- 所有 Claude 特有的功能都会尽可能转换为对应的 Gemini 功能
